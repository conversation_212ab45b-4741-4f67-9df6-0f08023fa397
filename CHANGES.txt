0.1.0, 10-02-17 - Initial release.
0.1.1, 11-02-17 - Now supports Python 3.x
0.1.2, 11-02-17 - Now supports Python on Windows machines
0.1.3, 11-02-17 - Now supports Python on Windows machines, minor changes
0.1.4, 12-02-17 - Guided main menu implemented that presented when CanvasSync is invoked without parameters
0.1.5, 13-02-17 - Files locked for the user will no longer be downloaded, fixed some Python 3 related errors
0.1.6, 08-03-17 - Minor bug fixes, add package version .py file
0.1.7, 09-05-17 - If HTML Pages mentions files in the HTML body, they are downloaded and stored with HTML in subfolder
0.1.8, 16-05-17 - Fixed a bug that would make CanvasSync crash if HTML pages were linked from within an assignment
0.1.9, 30-08-17 - Added command line arguments to sync and specify password (https://github.com/JackKiefer)
0.2.0, 15-09-17 - Fixed bug that would cause the program to crash if password was not specified through command line argument
0.2.1, 25-09-17 - Allows users to rename courses. Fixed a bug running CanvasSync on Arch Linux.
0.2.2, 13-08-18 - Changed dependency from pycrypto to pycryptodome, added (debugging) option for running the canvas.py script targeting the local package version if PyPi version is not installed, added temporary fix to download up to 100 files under each entity
0.2.3, 31-08-19 - Rename courses feature was poorly supported and has been removed. When installing via PIP, the entry script is now invoked with 'canvas' instead of 'canvas.py'. File- and folder names are now 'cleaned' for characters that are illegal in Windows and OneDrive sync processes on all operating systems (previously only on Windows). Fixed a various bugs. Minor refactoring performed.
0.2.4, 31-08-19 - Fixing minor error in packaging
