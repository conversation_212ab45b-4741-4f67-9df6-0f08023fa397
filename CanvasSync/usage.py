"""
CanvasSync by <PERSON>
February 2017

--------------------------------------------

usage.py, module

Implements the help function that displays the help prompt.

"""

# TODO
# Update the help section

# Future imports
from __future__ import print_function

# Inbuilt modules
import sys


def help():
    print(u"""
-------------------------
       CanvasSync
     <PERSON>
     February 2017--
-------------------------

CanvasSync helps students automatically synchronize modules, assignments & files located on their
institutions Canvas web server to a mirrored folder on their local computer.

Usage
-----
$ canvas.py [-S] [-h] [-s] [-i] <sync_path>

    sync_path                            : Required. Path to directory where settings and files will be stored.

    -h [--help], optional                : Show this help screen.

    -S [--sync], optional                : Synchronize with Canvas

    -s [--setup], optional               : Enter settings setup screen.

                                           The first time CanvasSync is launched settings must be set. Invoking
                                           CanvasSync with the -s or --setup flags will allow the user to reset
                                           these settings.

    -i [--info], optional                : Show currently active settings.

Setup
-----
CanvasSync requires at least the following settings to be set:

- A sync path (provided as command line argument). This folder will store the synced files and settings.
- A Canvas web server URL.
- An authentication token (see https://github.com/perslev/CanvasSync for details)
- A list of courses that should be synchronized.

CanvasSync will guide you through these settings during the first time launch. Alternatively,
the settings may be reset using the -s or --setup flag.

Examples:
- First time setup: canvas -s ~/Canvas
- Show current settings: canvas -i ~/Canvas
- Sync with existing settings: canvas ~/Canvas
- Force sync: canvas -S ~/Canvas

ADDITIONAL RESOURCES
--------------------
- Authentication token info and help:
  https://github.com/perslev/CanvasSync

- Canvas by Instructure home page
  https://canvas.instructure.com/

- Canvas LMS API documentation
  https://api.instructure.com
""")
    sys.exit(0)
