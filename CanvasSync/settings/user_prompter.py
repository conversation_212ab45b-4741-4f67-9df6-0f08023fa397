"""
CanvasSync by <PERSON>
February 2017

---------------------------------------------

user_prompter.py, module

A collection of functions used to prompt the user for settings.

"""

# TODO
# - Comments
# - Make a Y/N function to reduce code redundancy

# Future
from __future__ import print_function

# Inbuilt modules
import glob
import os

# Check for UNIX or Windows platform
try:
    import readline
    unix = True
except ImportError:
    unix = False

# If python 2.7, use raw_input(), otherwise use input()
from six.moves import input

# CanvasSync module import
from CanvasSync.utilities import helpers
from CanvasSync.utilities.ANSI import ANSI


def setup_settings(settings):
    """
    Prompt the user to set up the global settings.

    settings : Settings | A Settings object to configure

    Returns True if setup was successful, False otherwise.
    """
    # Show initial header/settings display
    user_prompter.print_settings(first_time_setup=True, clear=True)

    # Prompt user for domain
    settings.domain = ask_for_domain()
    user_prompter.print_settings(first_time_setup=True, clear=True)

    # Prompt user for auth token
    settings.token = ask_for_token(domain=settings.domain)
    user_prompter.print_settings(first_time_setup=True, clear=True)

    # Prompt user for course sync selection (only for global settings)
    if not settings.is_class_settings:
        settings.courses_to_sync = ask_for_courses(settings, api=settings.api)
    user_prompter.print_settings(first_time_setup=True, clear=True)

    # Ask user for advanced settings
    show_advanced = ask_for_advanced_settings(settings)

    if show_advanced:
        settings.modules_settings = ask_for_module_settings(settings.modules_settings, settings)
        settings.sync_assignments = ask_for_assignment_sync(settings)
        if not settings.sync_assignments:
            settings.download_linked = False
        else:
            settings.download_linked = ask_for_download_linked(settings)
        settings.avoid_duplicates = ask_for_avoid_duplicates(settings)

    return True

def setup_class_settings(class_path, global_settings):
    """
    Prompt the user to set up class-specific settings for a course.

    class_path : str | Path to the class folder
    global_settings : Settings | Global settings object to inherit from

    Returns a Settings object with class-specific settings, or None if cancelled.
    """
    import copy

    # Create a copy of global settings as the base
    class_settings = copy.deepcopy(global_settings)

    helpers.clear_console()
    print(ANSI.format(u"Class-Specific Settings Setup", u"announcer"))
    print(ANSI.format(u"=============================\n", u"announcer"))
    print(ANSI.format(u"You are setting up class-specific settings for:", u"white"))
    print(ANSI.format(u"  %s\n" % os.path.basename(class_path), u"blue"))
    print(ANSI.format(u"These settings will override the global settings for this course only.", u"white"))
    print(ANSI.format(u"Note: Course selection (courses_to_sync) cannot be overridden at the class level.\n", u"yellow"))

    # Ask if user wants to override authentication
    override_auth = ask_yes_no(u"Do you want to use different Canvas server/authentication for this class?")

    if override_auth:
        print(ANSI.format(u"\nEnter authentication settings for this class:", u"announcer"))
        class_settings.domain = ask_for_domain()
        class_settings.token = ask_for_token(domain=class_settings.domain)

    # Ask if user wants to override other settings
    override_other = ask_yes_no(u"Do you want to customize sync behavior for this class?")

    if override_other:
        print(ANSI.format(u"\nCustomizing sync behavior for this class:", u"announcer"))
        class_settings.modules_settings = ask_for_module_settings(class_settings.modules_settings, class_settings)
        class_settings.sync_assignments = ask_for_assignment_sync(class_settings)
        if not class_settings.sync_assignments:
            class_settings.download_linked = False
        else:
            class_settings.download_linked = ask_for_download_linked(class_settings)
        class_settings.avoid_duplicates = ask_for_avoid_duplicates(class_settings)
        class_settings.use_nicknames = ask_for_use_nicknames(class_settings)

    return class_settings

def ask_yes_no(question):
    """
    Ask a yes/no question and return True for yes, False for no.
    """
    while True:
        response = input(u"%s (y/n): " % question).lower().strip()
        if response in ['y', 'yes']:
            return True
        elif response in ['n', 'no']:
            return False
        else:
            print(ANSI.format(u"Please enter 'y' for yes or 'n' for no.", u"red"))


def print_advanced_settings(settings, clear=True):
    """
    Print the advanced settings currently in memory.
    Clear the console first if specified by the 'clear' parameter
    """
    if clear:
        helpers.clear_console()

    print(ANSI.format(u"\nAdvanced settings", u"announcer"))

    module_settings_string = ANSI.BOLD + u"[*] Sync module items:        \t" + ANSI.ENDC

    count = 0
    for item in settings.modules_settings:
        if settings.modules_settings[item]:
            d = u" & " if count != 0 else u""
            module_settings_string += d + ANSI.BLUE + item + ANSI.ENDC
            count += 1

    if count == 0:
        module_settings_string += ANSI.RED + u"False" + ANSI.ENDC

    print(module_settings_string)
    print(ANSI.BOLD + u"[*] Sync assignments:         \t" + ANSI.ENDC + (ANSI.GREEN if settings.sync_assignments else ANSI.RED) + str(settings.sync_assignments) + ANSI.ENDC)
    print(ANSI.BOLD + u"[*] Download linked files:    \t" + ANSI.ENDC + (ANSI.GREEN if settings.download_linked else ANSI.RED) + str(settings.download_linked) + ANSI.ENDC)
    print(ANSI.BOLD + u"[*] Avoid item duplicates:    \t" + ANSI.ENDC + (ANSI.GREEN if settings.avoid_duplicates else ANSI.RED) + str(settings.avoid_duplicates) + ANSI.ENDC)


def print_settings(settings, first_time_setup=True, clear=True):
    """
    Print the settings currently in memory.
    Clear the console first if specified by the 'clear' parameter
    """
    if clear:
        helpers.clear_console()

    if first_time_setup:
        print(ANSI.format(u"This is a first time setup.\nYou must specify "
                          u"at least the following settings"
                          u" in order to run CanvasSync:\n", u"announcer"))
    else:
        print(ANSI.format(u"-----------------------------", u"file"))
        print(ANSI.format(u"CanvasSync - Current settings", u"file"))
        print(ANSI.format(u"-----------------------------\n", u"file"))
        print(ANSI.format(u"Standard settings", u"announcer"))

    print(ANSI.BOLD + u"[*] Sync path:             \t" + ANSI.ENDC + ANSI.BLUE + (settings.sync_path if settings.sync_path else u"Not set") + ANSI.ENDC)
    print(ANSI.BOLD + u"[*] Canvas domain:         \t" + ANSI.ENDC + ANSI.BLUE + (settings.domain if settings.domain else u"Not set") + ANSI.ENDC)
    print(ANSI.BOLD + u"[*] Authentication token:  \t" + ANSI.ENDC + ANSI.BLUE + (settings.token if settings.token else u"Not set") + ANSI.ENDC)

    if settings.courses_to_sync and len(settings.courses_to_sync) > 0:
        print(ANSI.BOLD + u"[*] Courses to be synced:  \t1) " + ANSI.ENDC + ANSI.BLUE + settings.courses_to_sync[0] + ANSI.ENDC)

        for index, course in enumerate(settings.courses_to_sync[1:]):
            print(u" "*27 + u"\t%s) " % (index+2) + ANSI.BLUE + course + ANSI.ENDC)
    elif not settings.is_class_settings:
        print(ANSI.BOLD + u"[*] Courses to be synced:  \tNot set" + ANSI.ENDC)

def show_main_screen(settings_file_exists):
    """
    Prompt the user for initial choice of action. Does not allow Synchronization before settings file has been set
    """

    choice = -1
    to_do = "quit"
    while choice not in (0, 1, 2, 3, 4):
        helpers.clear_console()

        # Load version string
        import CanvasSync
        version = CanvasSync.__version__

        title = u"CanvasSync, "
        pretty_string = u"-" * (len(title) + len(version))

        print(ANSI.format(u"%s\n%s%s\n%s" % (pretty_string, title, version, pretty_string), u"file"))

        print(ANSI.format(u"Automatically synchronize modules, assignments & files located on a Canvas web server.",
                          u"announcer"))
        print(ANSI.format(u"\nWhat would you like to do?", u"underline"))
        print(u"\n\t1) " + ANSI.format(u"Synchronize my Canvas", u"blue"))
        print(u"\t2) " + ANSI.format(u"Set new settings", u"white"))
        print(u"\t3) " + ANSI.format(u"Show current settings", u"white"))
        print(u"\t4) " + ANSI.format(u"Show help", u"white"))
        print(u"\n\t0) " + ANSI.format(u"Quit", u"yellow"))

        try:
            choice = int(input(u"\nChoose number: "))
            if choice < 0 or choice > 4:
                continue
        except ValueError:
            continue

        if choice == 1 and not settings_file_exists:
            to_do = u"setup_settings"
        else:
            to_do = [u"quit", u"sync", u"setup_settings", u"show_settings", u"show_help"][choice]

    return to_do


def ask_for_sync_path():
    """
    Prompt the user for a path to a folder that will be used to synchronize the Canvas page into
    The path should point into a directory along with a sub-folder name of a folder not already existing.
    This folder wll be created using the os module.
    """

    # Enable auto-completion of path and cursor movement using the readline and glob modules
    def path_completer(text, state):
        if u"~" in text:
            text = text.replace(u"~", os.path.expanduser(u"~"))

        paths = glob.glob(u"%s*" % text)
        paths.append(False)

        return os.path.abspath(paths[state]) + u'/'

    if unix:
        readline.set_completer_delims(u' \t\n;')
        readline.parse_and_bind(u"tab: complete")
        readline.set_completer(path_completer)

    found = False
    # Keep asking until a valid path has been entered by the user
    while not found:
        sync_path = input(u"\nEnter a relative or absolute path to sync to (~/Desktop/Canvas etc.):\n$ ")

        # Expand tilde if present in the sync_path
        if u"~" in sync_path:
            sync_path = sync_path.replace(u"~", os.path.expanduser(u"~"))
        sync_path = os.path.abspath(sync_path)

        if not os.path.exists(os.path.split(sync_path)[0]):
            print(u"\n[ERROR] Base path '%s' does not exist." % os.path.split(sync_path)[0])
        else:
            found = True

    if unix:
        # Disable path auto-completer
        readline.parse_and_bind(u'set disable-completion on')

    return sync_path


def ask_for_domain():
    """
    Prompt the user for a Canvas domain.

    To ensure that the API calls are made on an encrypted SSL connection the initial 'https://' is pre-specified.
    To ensure that the user input is 1) a valid URL and 2) a URL representing a Canvas web server request is used
    to fetch a resources on the Canvas page. If the GET requests fails the URL was not valid. If the server returns
    a 404 unauthenticated error the domain is very likely to be a Canvas server, if anything else is returned the
    URL points to a correct URL that is not a Canvas server.
    """
    found = False

    # Keep asking until a valid domain has been entered by the user
    while not found:
        domain = u"https://" + input(u"\nEnter the Canvas domain of your institution:\n$ https://")
        found = helpers.validate_domain(domain)

    return domain


def ask_for_token(domain):
    """
    Prompt the user for an authentication token.

    The token must be generated on the Canvas web page when login in under the "Settings" menu.
    To ensure that the entered token is valid, a request GET call is made on a resource that requires authentication
    on the server. If the server responds with the resource the token is valid.
    """
    found = False

    # Keep asking until a valid authentication token has been entered by the user
    while not found:
        token = input(u"\nEnter authentication token (see 'Setup' section on https://github.com/perslev/CanvasSync for details):\n$ ")
        found = helpers.validate_token(domain, token)

    return token


def ask_for_courses(settings, api):

    courses = api.get_courses()
    courses = [course for course in courses if 'access_restricted_by_date' not in course]

    if settings.use_nicknames:
        courses = [name[u"name"] for name in courses if "name" in name]
    else:
        #  exclude courses that do not have a 'course_code'
        courses = [name[u"course_code"].split(";")[-1] for name in courses if 'course_code' in name
]

    choices = [True]*len(courses)

    choice = -1
    while choice != 0:
        settings.print_settings(clear=True)
        print(ANSI.format(u"\n\nPlease choose which courses you would like CanvasSync to sync for you:\n", u"white"))

        print(ANSI.format(u"Sync this item\tNumber\tCourse Title", u"blue"))
        for index, course in enumerate(courses):
            print(u"%s\t\t[%s]\t%s" % (ANSI.format(str(choices[index]), u"green" if choices[index] else u"red"),
                                          index+1, courses[index]))
        print(u"\n\n\t\t[%s]\t%s" % (0, ANSI.format(u"Confirm selection (at least one course required)", "blue")))
        print(u"\t\t[%s]\t%s" % (-1, ANSI.format(u"Select all", u"green")))
        print(u"\t\t[%s]\t%s" % (-2, ANSI.format(u"Deselect all", u"red")))

        try:
            choice = int(input(u"\nChoose number: "))
            if choice < -2 or choice > len(courses):
                continue
        except ValueError:
            continue

        if choice == 0:
            if sum(choices) == 0:
                choice = -1
                continue
            else:
                break
        elif choice == -1:
            choices = [True] * len(courses)
        elif choice == -2:
            choices = [False] * len(courses)
        else:
            choices[choice-1] = choices[choice-1] is not True

    print(choices)

    return [x for index, x in enumerate(courses) if choices[index]]


def ask_for_advanced_settings(settings):
    choice = -1
    while choice not in (1, 2):
        settings.print_settings(clear=True)

        print(ANSI.format(u"\n\nAll mandatory settings are set. Do you wish to configure advanced settings?", u"announcer"))

        print(ANSI.format(u"\n[1]\tShow advanced settings (recommended)", u"bold"))
        print(ANSI.format(u"[2]\tUse default settings", u"bold"))

        try:
            choice = int(input(u"\nChoose number: "))
        except ValueError:
            continue

        if choice == 1:
            return True
        elif choice == 2:
            return False
        else:
            continue





def ask_for_module_settings(module_settings, settings):
    choice = -1
    while choice != 0:
        settings.print_advanced_settings(clear=True)
        print(ANSI.format(u"\n\nModule settings", u"announcer"))
        print(ANSI.format(u"In Canvas, 'Modules' may contain various items such as files, HTML pages of\n"
                          u"exercises or reading material as well as links to external web-pages.\n\n"
                          u"Below you may specify, if you would like CanvasSync to avoid syncing some of these items.\n"
                          u"OBS: If you chose 'False' to all items, Modules will be skipped all together.", u"white"))

        print(ANSI.format(u"\nSync this item\tNumber\t\tItem", u"blue"))

        list_of_keys = list(module_settings.keys())
        for index, item in enumerate(list_of_keys):

            boolean = module_settings[item]

            print(u"%s\t\t[%s]\t\t%s" % (ANSI.format(str(boolean), u"green"
                                        if boolean else u"red"),
                                        index+1, item))

        print(u"\n\t\t[%s]\t\t%s" % (0, ANSI.format(u"Confirm selection", u"blue")))

        try:
            choice = int(input(u"\nChoose number: "))
            if choice < 0 or choice > len(module_settings):
                continue
        except ValueError:
            continue

        if choice == 0:
            break
        else:
            module_settings[list_of_keys[choice-1]] = module_settings[list_of_keys[choice-1]] is not True

    return module_settings


def ask_for_assignment_sync(settings):
    choice = -1

    while choice not in (1, 2):
        settings.print_advanced_settings(clear=True)
        print(ANSI.format(u"\n\nAssignments settings", u"announcer"))
        print(ANSI.format(u"Would you like CanvasSync to synchronize assignments?\n\n"
                          u"The assignment description will be downloaded as a HTML to be viewed offline\n"
                          u"and files hosted on the Canvas server that are described in the assignment\n"
                          u"description section will be downloaded to the same folder.\n", u"white"))

        print(ANSI.format(u"1) Sync assignments (default)", u"bold"))
        print(ANSI.format(u"2) Do not sync assignments", u"bold"))

        try:
            choice = int(input(u"\nChoose number: "))
        except ValueError:
            continue

        if choice == 1:
            return True
        elif choice == 2:
            return False
        else:
            continue


def ask_for_download_linked(settings):
    choice = -1

    while choice not in (1, 2):
        settings.print_advanced_settings(clear=True)
        print(ANSI.format(u"\n\nAssignments settings", u"announcer"))
        print(ANSI.format(u"You have chosen to synchronise assignments. URLs detected in the\n"
                          u"description field that point to files on Canvas will be downloaded\n"
                          u"to the assignment folder.\n\n"
                          u"CanvasSync may also attempt to download linked files that are NOT\n"
                          u"hosted on the Canvas server itself. CanvasSync is looking for URLs that\n"
                          u"end in a filename to avoid downloading other linked material such as\n"
                          u"web-sites. However, be aware that errors could occur.\n"
                          u"\nDo you wish to enable this feature?\n", u"white"))

        print(ANSI.format(u"1) Enable linked file downloading (default)", u"bold"))
        print(ANSI.format(u"2) Disable linked file downloading", u"bold"))

        try:
            choice = int(input(u"\nChoose number: "))
        except ValueError:
            continue

        if choice == 1:
            return True
        elif choice == 2:
            return False
        else:
            continue


def ask_for_avoid_duplicates(settings):
    choice = -1

    while choice not in (1, 2):
        settings.print_advanced_settings(clear=True)
        print(ANSI.format(u"\n\nVarious files settings", u"announcer"))
        print(ANSI.format(u"In addition to synchronizing modules and assignments,\n"
                          u"CanvasSync will sync files located under the 'Files'\n"
                          u"section in Canvas into a 'Various Files' folder.\n"
                          u"Often some of the files stored under 'Files' is mentioned in\n"
                          u"modules and assignments and may thus already exist in another\n"
                          u"folder after running CanvasSync.\n\n"
                          u"Do you want CanvasSync to avoid duplicates by only downloading\n"
                          u"files into the 'Various Files' folder, if they are not already\n"
                          u"present in one of the modules or assignments folders?\n", u"white"))

        print(ANSI.format(u"1) Yes, avoid duplicates (default)", u"bold"))
        print(ANSI.format(u"2) No, download all files to 'Various files'", u"bold"))

        try:
            choice = int(input(u"\nChoose number: "))
        except ValueError:
            continue

        if choice == 1:
            return True
        elif choice == 2:
            return False
        else:
            continue


def ask_for_use_nicknames(settings):
    """
    Prompt the user to choose whether to use course nicknames instead of course codes.
    """
    choice = -1
    while choice not in (1, 2):
        print_advanced_settings(settings, clear=True)
        print(ANSI.format(u"\n\nCourse naming settings", u"announcer"))
        print(ANSI.format(u"By default, CanvasSync uses course codes for folder names.\n"
                          u"You can choose to use course nicknames instead.\n", u"white"))

        print(ANSI.format(u"1) Use course codes (default)", u"bold"))
        print(ANSI.format(u"2) Use course nicknames", u"bold"))

        try:
            choice = int(input(u"\nChoose number: "))
        except ValueError:
            continue

        if choice == 1:
            return False
        elif choice == 2:
            return True
        else:
            continue


def write_class_settings(class_settings, class_path):
    """
    Write class-specific settings to .canvas.json in the class folder.
    Only writes settings that differ from global defaults.

    class_settings : Settings | The class settings object
    class_path : str | Path to the class folder

    Returns True if successful, False otherwise.
    """
    import json

    # Get the settings that can be overridden at class level
    overridable_settings = [
        u"domain", u"token", u"modules_settings", u"sync_assignments",
        u"download_linked", u"avoid_duplicates", u"use_nicknames"
    ]

    # Create settings data dictionary with only the overridable settings
    settings_data = {}
    for key in overridable_settings:
        if hasattr(class_settings, key):
            settings_data[key] = getattr(class_settings, key)

    # Write to .canvas.json in the class folder
    class_settings_path = os.path.join(class_path, u".canvas.json")

    try:
        with open(class_settings_path, u"w", encoding="utf-8") as out_file:
            json.dump(settings_data, out_file, indent=2, ensure_ascii=False)
        print(ANSI.format(u"\nClass settings saved to: %s" % class_settings_path, u"green"))
        return True
    except IOError as e:
        print(ANSI.format(u"\n[ERROR] Could not write class settings file: %s" % str(e), u"red"))
        return False
