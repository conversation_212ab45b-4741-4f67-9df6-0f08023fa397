"""
CanvasSync by <PERSON>
February 2017

--------------------------------------------

settings.py, Class

The Settings object implements the functionality of setting the initial-launch
settings and later loading these settings
These settings include:

1) A sync path provided via command line where synchronization will occur and
   where the settings file will be stored.
2) The domain of the Canvas web server.
3) An authentication token used to authenticate with the Canvas API. The token
   is generated on the Canvas web server
   after authentication under "Settings".

The Settings object will prompt the user for these settings through the
_prompt_setup_settings method and write them to a JSON file (.canvas.json) in the
sync path directory. Settings are stored in plain text JSON format.
"""

# TODO
# - Clean things
# - Implement ANKI.fomrat method instead of accessing the ANKI attributes
#   directly
# - Make it possible reuse settings, so that you do not have to
#   re-specify all settings to change a single one

# Future imports
from __future__ import print_function

# Inbuilt modules
import os
import sys
import json
import copy

# Third party modules
from six.moves import input

# CanvasSync modules
from CanvasSync.settings import user_prompter
from CanvasSync.utilities.instructure_api import InstructureApi
from CanvasSync.utilities.ANSI import ANSI
from CanvasSync.utilities import helpers


class Settings(object):
    def __init__(self, sync_path):
        self.api = None

        self.sync_path = sync_path
        self.settings_path = os.path.join(self.sync_path, u".canvas.json")
        self.is_class_settings = False

        self.domain = u""
        self.token = u""
        self.courses_to_sync = []
        self.modules_settings = {
            u"Files": True,
            u"HTML pages": True,
            u"External URLs": True
        }
        self.sync_assignments = True
        self.download_linked = True
        self.avoid_duplicates = True
        self.use_nicknames = False

    def _load_settings_json(self):
        """
        Load settings from a JSON file and return the data.
        Returns None if file doesn't exist or can't be loaded.
        """
        if not os.path.exists(self.setting_path):
            return None

        try:
            with open(self.setting_path, u"r", encoding="utf-8") as settings_f:
                return json.load(settings_f)
        except (IOError, ValueError, json.JSONDecodeError) as e:
            print(ANSI.format(u"\n[WARNING] Could not load settings file %s: %s" % (self.setting_path, str(e)), u"yellow"))
            return None

    def settings_file_exists(self):
        """
        Returns a boolean representing if the settings file
        has already been created in the sync path
        """
        return self.settings_path and os.path.exists(self.settings_path)

    def load_settings(self, force_setup=False):
        """
        Loads the current settings from the settings file and sets the
        attributes of the Settings object. Returns True if successful, False otherwise.
        """
        # If no settings file exists, prompt user to set them
        if force_setup or not self.settings_file_exists():
            if self._prompt_setup_settings():
                self.api = InstructureApi(self)
                return False
            else:
                return False

        if self.api is not None:
            return True

        # Load settings from the settings file
        settings_data = self._load_settings_json()
        if settings_data is None:
            print(ANSI.format(u"\n[ERROR] Could not load settings file.", u"announcer"))
            print(ANSI.format(u"        Please run with -s/--setup to reconfigure.", u"announcer"))
            return False

        # Apply all settings from the json
        self.domain = settings_data.get(u"domain", self.domain)
        self.token = settings_data.get(u"token", self.token)
        self.courses_to_sync = settings_data.get(u"courses_to_sync", self.courses_to_sync)
        self.modules_settings = settings_data.get(u"modules_settings", self.modules_settings)
        self.sync_assignments = settings_data.get(u"sync_assignments", self.sync_assignments)
        self.download_linked = settings_data.get(u"download_linked", self.download_linked)
        self.avoid_duplicates = settings_data.get(u"avoid_duplicates", self.avoid_duplicates)
        self.use_nicknames = settings_data.get(u"use_nicknames", self.use_nicknames)

        if not helpers.validate_token(self.domain, self.token):
            helpers.print_auth_token_reset_error()
            return False

        self.api = InstructureApi(self)
        return True

    def load_class_settings(self, class_path):
        """
        Loads per-class settings from .canvas.json in the class folder
        and returns a new Settings object with merged settings.

        class_path : str | Path to the class folder

        Returns a new Settings object with class-specific settings merged with global settings.
        Only 'courses_to_sync' cannot be overridden at the class level.
        """
        class_settings = copy.deepcopy(self)
        class_settings.api = None
        class_settings.courses_to_sync = None
        class_settings.sync_path = class_path
        class_settings.settings_path = os.path.join(class_path, u".canvas.json")
        class_settings.is_class_settings = True

        if not class_settings.load_settings():
            print(ANSI.format(u"[WARNING] Could not load class settings at %s" % class_settings.settings_path), u"yellow")
            return None
        return class_settings

    def _prompt_setup_settings(self):
        try:
            success = user_prompter.setup_settings(self)
            if not success:
                return False
        except KeyboardInterrupt:
            print(ANSI.format(u"\n\n[*] Setup interrupted, nothing was saved.", formatting=u"red"))
            sys.exit()

        self._write_settings()
        return True

    def _write_settings(self):
        """
        Write current settings to a JSON file.
        """
        user_prompter.print_settings(first_time_setup=False, clear=True)
        user_prompter.print_advanced_settings(clear=False)
        print(ANSI.format(u"\n\nThese settings will be saved to: %s" % self.settings_path, u"announcer"))

        # Create settings dictionary
        settings_data = {
            u"domain": self.domain,
            u"token": self.token,
            u"courses_to_sync": self.courses_to_sync,
            u"modules_settings": self.modules_settings,
            u"sync_assignments": self.sync_assignments,
            u"download_linked": self.download_linked,
            u"avoid_duplicates": self.avoid_duplicates,
            u"use_nicknames": self.use_nicknames
        }

        # Write settings as JSON to the settings path
        try:
            with open(self.settings_path, u"w", encoding="utf-8") as out_file:
                json.dump(settings_data, out_file, indent=2, ensure_ascii=False)
            print(ANSI.format(u"Settings saved successfully!", u"announcer"))
        except IOError as e:
            print(ANSI.format(u"\n[ERROR] Could not write settings file: %s" % str(e), u"announcer"))

    def show(self, quit=True):
        """
        Show the current settings
        If quit=True, sys.exit after user confirmation
        """
        self.load_settings()
        user_prompter.print_settings(first_time_setup=False, clear=True)
        user_prompter.print_advanced_settings(clear=False)

        if quit:
            sys.exit()
        else:
            input(u"\nHit enter to continue.")
