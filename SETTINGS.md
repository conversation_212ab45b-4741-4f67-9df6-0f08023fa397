# Per-Class Settings Documentation

## Overview

CanvasSync now supports per-class settings that allow you to override global settings for individual courses. This feature enables you to customize synchronization behavior on a per-course basis while maintaining global defaults.

## How It Works

### Global Settings
- Global settings are stored in `.canvas.json` in the main sync directory
- These settings apply to all courses by default
- Contains authentication, course selection, and default sync preferences

### Per-Class Settings
- Per-class settings are stored in `.canvas.json` within each course folder
- These settings override global settings for that specific course only
- Only certain settings can be overridden at the class level

## Supported Per-Class Settings

The following settings can be overridden at the class level:

### Authentication Settings
- `domain`: Canvas server domain (e.g., "https://myschool.instructure.com")
- `token`: Authentication token for the Canvas API

### Module Settings
- `modules_settings`: Controls which types of content to sync
  - `Files`: Sync file attachments
  - `HTML pages`: Sync HTML page content
  - `External URLs`: Sync external URL links

### Sync Behavior Settings
- `sync_assignments`: Whether to sync assignments (true/false)
- `download_linked`: Whether to download linked files in assignments (true/false)
- `avoid_duplicates`: Whether to avoid downloading duplicate files (true/false)
- `use_nicknames`: Whether to use course nicknames instead of course codes (true/false)

## Global-Only Settings

The following setting **cannot** be overridden at the class level:

- `courses_to_sync`: List of courses to synchronize (must be configured globally)

## Example Usage

### Global Settings (`.canvas.json`)
```json
{
  "domain": "https://myschool.instructure.com",
  "token": "global_auth_token_123",
  "courses_to_sync": ["Math 101", "Physics 201", "Chemistry 301"],
  "modules_settings": {
    "Files": true,
    "HTML pages": true,
    "External URLs": false
  },
  "sync_assignments": true,
  "download_linked": true,
  "avoid_duplicates": true,
  "use_nicknames": false
}
```

### Per-Class Settings (`Math 101/.canvas.json`)
```json
{
  "domain": "https://math-dept.instructure.com",
  "token": "math_specific_token_456",
  "modules_settings": {
    "Files": true,
    "HTML pages": false,
    "External URLs": true
  },
  "sync_assignments": false,
  "use_nicknames": true
}
```

In this example:
- Math 101 will use a different Canvas server and authentication token
- Math 101 will not sync HTML pages but will sync external URLs
- Math 101 will not sync assignments
- Math 101 will use course nicknames instead of course codes
- All other settings (download_linked, avoid_duplicates) will inherit from global settings

## Setting Up Per-Class Settings

1. **Ensure the course is in your global `courses_to_sync` list**
   - Per-class settings only apply to courses that are configured to sync globally

2. **Create the course folder**
   - Run CanvasSync once to create the course folder structure

3. **Create `.canvas.json` in the course folder**
   - Place this file directly in the course folder (e.g., `~/Canvas/Math 101/.canvas.json`)
   - Include only the settings you want to override

4. **Run CanvasSync**
   - The system will automatically detect and load per-class settings
   - You'll see a message: `[INFO] Loaded class-specific settings for [Course Name]`

## Use Cases

### Different Canvas Servers
Some institutions use multiple Canvas instances. You can configure different courses to sync from different servers:

```json
{
  "domain": "https://graduate.myschool.edu",
  "token": "graduate_token_789"
}
```

### Course-Specific Content Preferences
Different courses may have different content types that are useful:

```json
{
  "modules_settings": {
    "Files": true,
    "HTML pages": false,
    "External URLs": true
  },
  "sync_assignments": false
}
```

### Instructor-Specific Settings
If you're an instructor with different preferences for your own courses:

```json
{
  "use_nicknames": true,
  "avoid_duplicates": false,
  "download_linked": false
}
```

## Technical Implementation

The per-class settings system:

1. **Loads global settings first** as the base configuration
2. **Creates a deep copy** of global settings for each course
3. **Loads per-class settings** from `.canvas.json` if it exists
4. **Merges settings** by overriding only the specified values
5. **Preserves global-only settings** like `courses_to_sync`
6. **Creates separate API objects** when authentication differs

## Troubleshooting

### Settings Not Taking Effect
- Ensure `.canvas.json` is in the correct course folder
- Check that the JSON syntax is valid
- Verify the course is listed in global `courses_to_sync`

### Authentication Issues
- Verify the domain URL is correct (include https://)
- Ensure the token has appropriate permissions for the target Canvas instance
- Check that the token hasn't expired

### File Not Found Warnings
- If you see warnings about missing class settings files, this is normal
- The system will fall back to global settings automatically

## Migration

Existing CanvasSync installations will continue to work without changes. Per-class settings are entirely optional and only take effect when `.canvas.json` files are present in course folders.
